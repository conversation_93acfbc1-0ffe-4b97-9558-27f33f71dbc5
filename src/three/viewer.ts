/*
 * @Description:
 * @Author: yucheng
 * @Date: 2025-06-18
 * @LastEditors: '余承'
 * @LastEditTime: 2025-07-13
 */

import { sceneConfig } from '@/config/scene'
import * as THREE from 'three'
import { OrbitControls } from 'three/addons/controls/OrbitControls.js'
import { threeJSEvents, initThreeJSEvents } from './evens'
import { initHighlightManager } from './highLight'
import { labelManager } from './label'
import { EXRLoader } from 'three/addons/loaders/EXRLoader.js'
import { WeatherSystem } from './weather'

class Viewer {
  public scene: THREE.Scene
  public camera: THREE.PerspectiveCamera
  public renderer: THREE.WebGLRenderer
  public controls: OrbitControls
  public weatherSystem: WeatherSystem
  private container: HTMLDivElement
  private renderCallbacks: Array<() => void> = []
  private animateId: number | null = null

  constructor(container: HTMLDivElement) {
    this.container = container
    this.scene = this.createScene()
    this.camera = this.createCamera()
    this.renderer = this.createRenderer()
    this.controls = this.createControls()
    this.weatherSystem = new WeatherSystem(this.scene, this.camera)

    this.setupLights()
    this.setupHelpers()
    this.setupEventListeners()
    this.setupSkybox()

    // 添加天气系统的更新到渲染回调
    this.addRenderCallback(() => {
      this.weatherSystem.update()
    })
    this.animate()
  }

  private createScene(): THREE.Scene {
    const scene = new THREE.Scene()
    return scene
  }

  private createCamera(): THREE.PerspectiveCamera {
    const camera = new THREE.PerspectiveCamera(
      sceneConfig.fov,
      this.container.clientWidth / this.container.clientHeight,
      0.1,
      10000,
    )
    camera.position.set(...sceneConfig.center.position)
    return camera
  }

  private createRenderer(): THREE.WebGLRenderer {
    const renderer = new THREE.WebGLRenderer({
      antialias: true,
      alpha: true,
    })
    renderer.setSize(this.container.clientWidth, this.container.clientHeight)
    renderer.setPixelRatio(window.devicePixelRatio)
    renderer.toneMapping = THREE.ACESFilmicToneMapping
    renderer.toneMappingExposure = 0.35
    renderer.outputColorSpace = THREE.SRGBColorSpace
    this.container.appendChild(renderer.domElement)
    return renderer
  }

  private createControls(): OrbitControls {
    const controls = new OrbitControls(this.camera, this.renderer.domElement)
    controls.enableDamping = true
    controls.dampingFactor = 0.05

    // 设置鼠标按键映射
    controls.mouseButtons = {
      LEFT: THREE.MOUSE.PAN, // 左键平移
      RIGHT: THREE.MOUSE.ROTATE, // 右键旋转
      MIDDLE: THREE.MOUSE.DOLLY, // 中键缩放
    }
    controls.minPolarAngle = 0
    controls.maxPolarAngle = 1.4
    controls.target.set(...sceneConfig.center.target)
    return controls
  }

  private setupLights(): void {
    const ambientLight = new THREE.AmbientLight(0xffffff, 1)
    this.scene.add(ambientLight)

    const sunLight = new THREE.DirectionalLight(0xffffff, 1)
    sunLight.position.set(-346.92977631294156, 5400, -2.7176653045220274) // 光源位置（模拟太阳方向）
    sunLight.castShadow = true // 启用阴影

    // 优化阴影质量（重要！）
    sunLight.shadow.mapSize.width = 4096
    sunLight.shadow.mapSize.height = 4096
    sunLight.shadow.camera.near = 0.5
    sunLight.shadow.camera.far = 500
    sunLight.shadow.camera.left = -50
    sunLight.shadow.camera.right = 50
    sunLight.shadow.camera.top = 50
    sunLight.shadow.camera.bottom = -50
    this.scene.add(sunLight)
  }

  private setupHelpers(): void {
    // const axesHelper = new THREE.AxesHelper(100)
    // this.scene.add(axesHelper)
    // const gridHelper = new THREE.GridHelper(100, 100)
    // this.scene.add(gridHelper)
  }

  private setupSkybox(): void {
    const pmremGenerator = new THREE.PMREMGenerator(this.renderer)
    pmremGenerator.compileEquirectangularShader()

    new EXRLoader().load(
      '/skybox/skybox.exr',
      (texture) => {
        const envMap = pmremGenerator.fromEquirectangular(texture).texture
        this.scene.background = envMap
        this.scene.environment = envMap

        texture.dispose()
        pmremGenerator.dispose()

        // 发布天空盒加载完成事件
        threeJSEvents.publish('skybox:loaded', { texture: envMap })
      },
      undefined,
      (error) => {
        console.error('加载天空盒失败:', error)
        // 发布天空盒加载失败事件
        threeJSEvents.publish('skybox:error', { error })
      },
    )
  }

  private setupEventListeners(): void {
    window.addEventListener('resize', () => {
      this.camera.aspect = this.container.clientWidth / this.container.clientHeight
      this.camera.updateProjectionMatrix()
      this.renderer.setSize(this.container.clientWidth, this.container.clientHeight)

      // 触发场景大小变化事件
      threeJSEvents.publish('scene:resize', {
        width: this.container.clientWidth,
        height: this.container.clientHeight,
      })
    })
  }

  public animate = (): void => {
    this.controls.update()

    // 执行所有注册的渲染回调函数
    for (const callback of this.renderCallbacks) {
      callback()
    }
    this.renderer.render(this.scene, this.camera)
    requestAnimationFrame(this.animate)
  }

  /**
   * 添加渲染回调函数
   * @param callback 在每帧渲染前执行的回调函数
   * @returns 回调函数索引，可用于移除回调
   */
  public addRenderCallback(callback: () => void): number {
    this.renderCallbacks.push(callback)
    return this.renderCallbacks.length - 1
  }

  /**
   * 移除渲染回调函数
   * @param index 回调函数索引
   * @returns 是否成功移除
   */
  public removeRenderCallback(index: number): boolean {
    if (index >= 0 && index < this.renderCallbacks.length) {
      this.renderCallbacks.splice(index, 1)
      return true
    }
    return false
  }

  public getScene(): THREE.Scene {
    return this.scene
  }

  public getCamera(): THREE.PerspectiveCamera {
    return this.camera
  }

  public getRenderer(): THREE.WebGLRenderer {
    return this.renderer
  }

  public getControls(): OrbitControls {
    return this.controls
  }

  /**
   * 加载模型
   * @param url 模型URL
   * @param onProgress 加载进度回调
   * @returns Promise<THREE.Object3D>
   */
  public loadModel(
    url: string,
    onProgress?: (event: ProgressEvent) => void,
  ): Promise<THREE.Object3D> {
    return new Promise((resolve, reject) => {
      const loader = new THREE.ObjectLoader()

      loader.load(
        url,
        (object) => {
          this.scene.add(object)
          // 发布模型加载完成事件
          threeJSEvents.modelLoaded(object, url)
          resolve(object)
        },
        onProgress,
        (error) => {
          // 发布模型加载失败事件
          const err = error instanceof Error ? error : new Error(String(error))
          threeJSEvents.modelError(err, url)
          reject(err)
        },
      )
    })
  }

  /**
   * 注册事件监听
   * @param eventName 事件名称
   * @param callback 回调函数
   * @param once 是否只触发一次
   */
  public on<K extends keyof import('./evens').ThreeJSEventMap>(
    eventName: K,
    callback: (data: import('./evens').ThreeJSEventMap[K]) => void,
    once = false,
  ): void {
    threeJSEvents.subscribe(eventName, callback, once)
  }

  /**
   * 取消事件监听
   * @param eventName 事件名称
   * @param callback 回调函数
   */
  public off<K extends keyof import('./evens').ThreeJSEventMap>(
    eventName: K,
    callback: (data: import('./evens').ThreeJSEventMap[K]) => void,
  ): void {
    threeJSEvents.unsubscribe(eventName, callback)
  }
}

export let viewer: Viewer

export const initViewer = (container: HTMLDivElement) => {
  viewer = new Viewer(container)
  initThreeJSEvents()
  initHighlightManager()
  // 初始化标签管理器
  labelManager.initialize(container)
  // @ts-ignore
  window.viewer = viewer
}
