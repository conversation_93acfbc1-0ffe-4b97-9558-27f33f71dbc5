import {
  TextureLoader,
  LoadingManager,
  ObjectLoader,
  AudioLoader,
  Texture,
  Object3D,
  Group,
  Box3,
  Vector3,
  Sphere,
} from 'three'
import { GLTFLoader } from 'three/addons/loaders/GLTFLoader.js'
import type { GLTF } from 'three/addons/loaders/GLTFLoader.js'
import { viewer } from './viewer'

type ResourceType = 'texture' | 'object' | 'audio' | 'gltf'
type ResourceItem = Texture | Object3D | AudioBuffer | Group

interface ResourceCache {
  [key: string]: {
    type: ResourceType
    resource: ResourceItem
  }
}

export class ResourceManager {
  private static instance: ResourceManager
  private loadingManager: LoadingManager
  private textureLoader: TextureLoader
  private objectLoader: ObjectLoader
  private audioLoader: AudioLoader
  private gltfLoader: GLTFLoader
  private cache: ResourceCache = {}

  private constructor() {
    this.loadingManager = new LoadingManager()
    this.textureLoader = new TextureLoader(this.loadingManager)
    this.objectLoader = new ObjectLoader(this.loadingManager)
    this.audioLoader = new AudioLoader(this.loadingManager)
    this.gltfLoader = new GLTFLoader(this.loadingManager)

    // 设置加载管理器的回调
    this.loadingManager.onProgress = (url, loaded, total) => {
      // console.log(`Loading resource: ${url}. ${loaded} of ${total} files.`)
    }

    this.loadingManager.onError = (url) => {
      console.error(`Error loading resource: ${url}`)
    }
  }

  public static getInstance(): ResourceManager {
    if (!ResourceManager.instance) {
      ResourceManager.instance = new ResourceManager()
    }
    return ResourceManager.instance
  }

  // 加载纹理
  public async loadTexture(url: string): Promise<Texture> {
    const cached = this.getFromCache(url)
    if (cached && cached.type === 'texture') {
      return cached.resource as Texture
    }

    return new Promise((resolve, reject) => {
      this.textureLoader.load(
        url,
        (texture) => {
          this.addToCache(url, 'texture', texture)
          resolve(texture)
        },
        undefined,
        (error: unknown) => reject(error),
      )
    })
  }

  // 加载 GLTF 模型
  public async loadGLTF(url: string): Promise<Group> {
    const cached = this.getFromCache(url)
    if (cached && cached.type === 'gltf') {
      return cached.resource as Group
    }

    return new Promise((resolve, reject) => {
      this.gltfLoader.load(
        url,
        (gltf: GLTF) => {
          this.addToCache(url, 'gltf', gltf.scene)
          console.log(4444, url, gltf.scene)

          resolve(gltf.scene)
        },
        undefined,
        (error: unknown) => reject(error),
      )
    })
  }

  // 加载普通 3D 对象
  public async loadObject(url: string): Promise<Object3D> {
    const cached = this.getFromCache(url)
    if (cached && cached.type === 'object') {
      return cached.resource as Object3D
    }

    return new Promise((resolve, reject) => {
      this.objectLoader.load(
        url,
        (object) => {
          this.addToCache(url, 'object', object)
          resolve(object)
        },
        undefined,
        (error: unknown) => reject(error),
      )
    })
  }

  // 加载音频
  public async loadAudio(url: string): Promise<AudioBuffer> {
    const cached = this.getFromCache(url)
    if (cached && cached.type === 'audio') {
      return cached.resource as AudioBuffer
    }

    return new Promise((resolve, reject) => {
      this.audioLoader.load(
        url,
        (buffer) => {
          this.addToCache(url, 'audio', buffer)
          resolve(buffer)
        },
        undefined,
        (error: unknown) => reject(error),
      )
    })
  }

  // 从缓存中获取资源
  public getFromCache(url: string) {
    return this.cache[url]
  }

  // 添加资源到缓存
  private addToCache(url: string, type: ResourceType, resource: ResourceItem) {
    this.cache[url] = { type, resource }
  }

  // 清除指定资源的缓存
  public clearCache(url: string) {
    delete this.cache[url]
  }

  // 清除所有缓存
  public clearAllCache() {
    this.cache = {}
  }

  // 获取加载管理器
  public getLoadingManager(): LoadingManager {
    return this.loadingManager
  }
}

// 导出单例实例
export const resourceManager = ResourceManager.getInstance()
