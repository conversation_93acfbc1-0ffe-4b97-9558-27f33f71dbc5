import dayjs from 'dayjs'

/**
 * @description: 获取assets图片
 * @param {string} name 图片名称
 * @returns {string} 图片路径
 */
export function getAssetsImage(name: string, folder = 'overview') {
  return new URL(`/src/assets/images/${folder}/${name}`, import.meta.url).href
}

type CountdownOptions = {
  seconds: number
  format?: 'HH:mm:ss' | 'mm:ss' | 'ss'
  onTick?: (data: { secondsLeft: number; formatted: string }) => void
  onFinish?: () => void
  onStart?: () => void
  onPause?: () => void
  onResume?: () => void
}

type CountdownController = {
  stop: () => void
  pause: () => void
  resume: () => void
  reset: () => void
  isRunning: () => boolean
  isPaused: () => boolean
  getRemainingTime: () => number
}

/**
 * @description: 格式化时间
 * @param time 时间
 * @param format 格式
 * @returns 格式化后的时间
 */
export function formatTime(time: dayjs.ConfigType, format = 'HH:mm:ss'): string {
  return dayjs(time).format(format)
}

/**
 * @description: 格式化倒计时秒数
 * @param seconds 秒数
 * @param format 格式
 * @returns 格式化后的时间字符串
 */
export function formatCountdownTime(
  seconds: number,
  format: 'HH:mm:ss' | 'mm:ss' | 'ss' = 'HH:mm:ss',
): string {
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const secs = seconds % 60

  const pad = (num: number) => num.toString().padStart(2, '0')

  switch (format) {
    case 'HH:mm:ss':
      return `${pad(hours)}:${pad(minutes)}:${pad(secs)}`
    case 'mm:ss':
      return `${pad(minutes + hours * 60)}:${pad(secs)}`
    case 'ss':
      return pad(seconds)
    default:
      return `${pad(hours)}:${pad(minutes)}:${pad(secs)}`
  }
}

/**
 * @description: 倒计时
 * @param options 倒计时选项
 * @returns 倒计时控制器
 */
export function countdown(options: CountdownOptions): CountdownController {
  const { seconds, format = 'HH:mm:ss', onTick, onFinish, onStart, onPause, onResume } = options

  // 参数验证
  if (seconds <= 0) {
    throw new Error('倒计时秒数必须大于0')
  }

  let remaining = seconds
  let timer: number | null = null
  let isRunning = false
  let isPaused = false
  const initialSeconds = seconds

  const tick = () => {
    if (remaining <= 0) {
      stop()
      onFinish?.()
      return
    }

    onTick?.({
      secondsLeft: remaining,
      formatted: formatCountdownTime(remaining, format),
    })

    remaining--
  }

  const start = () => {
    if (isRunning && !isPaused) return

    isRunning = true
    isPaused = false
    onStart?.()

    tick() // 立即执行一次
    timer = window.setInterval(tick, 1000)
  }

  const stop = () => {
    if (timer) {
      clearInterval(timer)
      timer = null
    }
    isRunning = false
    isPaused = false
  }

  const pause = () => {
    if (!isRunning || isPaused) return

    if (timer) {
      clearInterval(timer)
      timer = null
    }
    isPaused = true
    onPause?.()
  }

  const resume = () => {
    if (!isPaused) return

    isPaused = false
    onResume?.()
    timer = window.setInterval(tick, 1000)
  }

  const reset = () => {
    stop()
    remaining = initialSeconds
  }

  // 自动开始
  start()

  return {
    stop,
    pause,
    resume,
    reset,
    isRunning: () => isRunning && !isPaused,
    isPaused: () => isPaused,
    getRemainingTime: () => remaining,
  }
}
