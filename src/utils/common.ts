import dayjs from 'dayjs'

/**
 * @description: 获取assets图片
 * @param {string} name 图片名称
 * @returns {string} 图片路径
 */
export function getAssetsImage(name: string, folder = 'overview') {
  return new URL(`/src/assets/images/${folder}/${name}`, import.meta.url).href
}

type CountdownOptions = {
  seconds: number
  format?: string
  onTick?: (data: { secondsLeft: number; formatted: string }) => void
  onFinish?: () => void
}

/**
 * @description: 格式化时间
 * @param {dayjs.ConfigType} time 时间
 * @param {string} format 格式
 * @returns {string} 格式化后的时间
 */
export function formatTime(time: dayjs.ConfigType, format = 'HH:mm:ss'): string {
  return dayjs(time).format(format)
}

/**
 * @description: 倒计时
 * @param {CountdownOptions} options 倒计时选项
 * @returns {() => void} 倒计时函数
 */
export function countdown(options: CountdownOptions): () => void {
  const { seconds, format, onTick, onFinish } = options

  let remaining = seconds
  let timer: number

  const tick = () => {
    if (remaining <= 0) {
      clearInterval(timer)
      onFinish?.()
      return
    }

    onTick?.({
      secondsLeft: remaining,
      formatted: formatTime(remaining, format),
    })

    remaining--
  }

  tick() // 立即执行一次
  timer = window.setInterval(tick, 1000)

  // 提供中途停止功能
  return () => clearInterval(timer)
}
