import { countdown } from '../utils/common'
import dayjs from 'dayjs'

// 基础使用示例 - 秒数倒计时
export function basicCountdownExample() {
  const controller = countdown({
    seconds: 60,
    format: 'mm:ss',
    onTick: ({ secondsLeft, formatted, timeLeft, currentTime }) => {
      console.log(`剩余时间: ${formatted} (${secondsLeft}秒)`)
      console.log(`详细时间: ${timeLeft.minutes}分${timeLeft.seconds}秒`)
      console.log(`当前时间: ${currentTime.format('HH:mm:ss')}`)
    },
    onFinish: () => {
      console.log('倒计时结束!')
    },
    onStart: () => {
      console.log('倒计时开始')
    },
  })

  return controller
}

// 目标时间倒计时示例
export function targetTimeCountdownExample() {
  // 倒计时到明天上午9点
  const tomorrow9AM = dayjs().add(1, 'day').hour(9).minute(0).second(0)

  const controller = countdown({
    targetTime: tomorrow9AM,
    format: 'D天HH:mm:ss',
    onTick: ({ secondsLeft, formatted, timeLeft, targetTime, currentTime }) => {
      console.log(`距离明天9点还有: ${formatted}`)
      console.log(
        `详细: ${timeLeft.days}天${timeLeft.hours}时${timeLeft.minutes}分${timeLeft.seconds}秒`,
      )
      console.log(`目标时间: ${targetTime?.format('YYYY-MM-DD HH:mm:ss')}`)
      console.log(`当前时间: ${currentTime.format('YYYY-MM-DD HH:mm:ss')}`)
    },
    onFinish: () => {
      console.log('到达目标时间!')
    },
    onStart: () => {
      console.log('开始倒计时到明天9点')
    },
  })

  return controller
}

// 中文格式倒计时示例
export function chineseFormatExample() {
  const controller = countdown({
    seconds: 3661, // 1小时1分1秒
    format: 'H时m分s秒',
    onTick: ({ formatted, timeLeft }) => {
      console.log(`中文格式: ${formatted}`)
      console.log(`对象格式: ${timeLeft.hours}时${timeLeft.minutes}分${timeLeft.seconds}秒`)
    },
    onFinish: () => {
      console.log('中文倒计时结束!')
    },
  })

  return controller
}

// 高级使用示例 - 考试倒计时
export function examCountdownExample() {
  // 考试结束时间：当前时间 + 2小时
  const examEndTime = dayjs().add(2, 'hour')

  const controller = countdown({
    targetTime: examEndTime,
    format: 'HH:mm:ss',
    onTick: ({ secondsLeft, formatted, timeLeft, targetTime, currentTime }) => {
      // 更新UI显示
      updateExamTimer(formatted)

      // 显示详细信息
      console.log(`考试剩余: ${timeLeft.hours}时${timeLeft.minutes}分${timeLeft.seconds}秒`)
      console.log(`考试结束时间: ${targetTime?.format('HH:mm:ss')}`)

      // 剩余30分钟时警告
      if (secondsLeft === 1800) {
        showWarning('考试剩余30分钟!')
      }

      // 剩余5分钟时警告
      if (secondsLeft === 300) {
        showWarning('考试剩余5分钟!')
      }

      // 剩余1分钟时最后警告
      if (secondsLeft === 60) {
        showWarning('考试剩余1分钟!')
      }
    },
    onFinish: () => {
      // 自动提交考试
      submitExam()
      showMessage('考试时间结束，已自动提交!')
    },
    onStart: () => {
      showMessage(`考试开始! 结束时间: ${examEndTime.format('HH:mm:ss')}`)
    },
  })

  // 提供考试控制功能
  return {
    stopExam: () => controller.stop(),
    resetExam: () => controller.reset(),
    getRemainingTime: () => controller.getRemainingTime(),
    getTargetTime: () => controller.getTargetTime(),
    getCurrentTime: () => controller.getCurrentTime(),
    isRunning: () => controller.isRunning(),
  }
}

// 活动倒计时示例
export function eventCountdownExample() {
  // 活动开始时间：今天晚上8点
  const eventTime = dayjs().hour(20).minute(0).second(0)
  // 如果已经过了今天8点，则设为明天8点
  const targetTime = eventTime.isBefore(dayjs()) ? eventTime.add(1, 'day') : eventTime

  const controller = countdown({
    targetTime,
    format: 'D天H时m分s秒',
    onTick: ({ formatted, timeLeft, targetTime }) => {
      console.log(`距离活动开始: ${formatted}`)
      if (timeLeft.days > 0) {
        console.log(`还有 ${timeLeft.days} 天`)
      } else if (timeLeft.hours > 0) {
        console.log(`今天 ${targetTime?.format('HH:mm')} 开始`)
      } else {
        console.log(`即将开始!`)
      }
    },
    onFinish: () => {
      console.log('活动开始了!')
    },
  })

  return controller
}

// 模拟函数
function updateExamTimer(time: string) {
  console.log(`考试剩余时间: ${time}`)
}

function showWarning(message: string) {
  console.warn(message)
}

function showMessage(message: string) {
  console.log(message)
}

function submitExam() {
  console.log('正在提交考试...')
}

// 多个倒计时管理示例
export function multipleCountdownExample() {
  const countdowns = new Map<string, ReturnType<typeof countdown>>()

  // 创建多个倒计时
  const createCountdown = (id: string, seconds: number, description: string) => {
    const controller = countdown({
      seconds,
      format: 'mm:ss',
      onTick: ({ formatted }) => {
        console.log(`${description}: ${formatted}`)
      },
      onFinish: () => {
        console.log(`${description} 完成!`)
        countdowns.delete(id)
      },
      onStart: () => {
        console.log(`${description} 开始`)
      },
    })

    countdowns.set(id, controller)
    return controller
  }

  // 停止所有倒计时
  const stopAll = () => {
    countdowns.forEach((controller) => controller.stop())
    countdowns.clear()
  }

  // 重置所有倒计时
  const resetAll = () => {
    countdowns.forEach((controller) => controller.reset())
  }

  return {
    createCountdown,
    stopAll,
    resetAll,
    getActiveCount: () => countdowns.size,
    getRunningCount: () => {
      let count = 0
      countdowns.forEach((controller) => {
        if (controller.isRunning()) count++
      })
      return count
    },
  }
}
