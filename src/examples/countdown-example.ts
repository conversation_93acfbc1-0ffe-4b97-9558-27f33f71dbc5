import { countdown } from '../utils/common'

// 基础使用示例
export function basicCountdownExample() {
  const controller = countdown({
    seconds: 60,
    format: 'mm:ss',
    onTick: ({ secondsLeft, formatted }) => {
      console.log(`剩余时间: ${formatted} (${secondsLeft}秒)`)
    },
    onFinish: () => {
      console.log('倒计时结束!')
    },
    onStart: () => {
      console.log('倒计时开始')
    },
  })

  // 30秒后重置
  setTimeout(() => {
    controller.reset()
  }, 30000)

  // 45秒后停止
  setTimeout(() => {
    controller.stop()
  }, 45000)

  return controller
}

// 高级使用示例 - 考试倒计时
export function examCountdownExample() {
  const examDuration = 3600 // 1小时考试

  const controller = countdown({
    seconds: examDuration,
    format: 'HH:mm:ss',
    onTick: ({ secondsLeft, formatted }) => {
      // 更新UI显示
      updateExamTimer(formatted)

      // 剩余5分钟时警告
      if (secondsLeft === 300) {
        showWarning('考试剩余5分钟!')
      }

      // 剩余1分钟时最后警告
      if (secondsLeft === 60) {
        showWarning('考试剩余1分钟!')
      }
    },
    onFinish: () => {
      // 自动提交考试
      submitExam()
      showMessage('考试时间结束，已自动提交!')
    },
    onStart: () => {
      showMessage('考试开始!')
    },
  })

  // 提供考试控制功能
  return {
    stopExam: () => controller.stop(),
    resetExam: () => controller.reset(),
    getRemainingTime: () => controller.getRemainingTime(),
    isRunning: () => controller.isRunning(),
  }
}

// 模拟函数
function updateExamTimer(time: string) {
  console.log(`考试剩余时间: ${time}`)
}

function showWarning(message: string) {
  console.warn(message)
}

function showMessage(message: string) {
  console.log(message)
}

function submitExam() {
  console.log('正在提交考试...')
}

// 多个倒计时管理示例
export function multipleCountdownExample() {
  const countdowns = new Map<string, ReturnType<typeof countdown>>()

  // 创建多个倒计时
  const createCountdown = (id: string, seconds: number, description: string) => {
    const controller = countdown({
      seconds,
      format: 'mm:ss',
      onTick: ({ formatted }) => {
        console.log(`${description}: ${formatted}`)
      },
      onFinish: () => {
        console.log(`${description} 完成!`)
        countdowns.delete(id)
      },
      onStart: () => {
        console.log(`${description} 开始`)
      },
    })

    countdowns.set(id, controller)
    return controller
  }

  // 停止所有倒计时
  const stopAll = () => {
    countdowns.forEach((controller) => controller.stop())
    countdowns.clear()
  }

  // 重置所有倒计时
  const resetAll = () => {
    countdowns.forEach((controller) => controller.reset())
  }

  return {
    createCountdown,
    stopAll,
    resetAll,
    getActiveCount: () => countdowns.size,
    getRunningCount: () => {
      let count = 0
      countdowns.forEach((controller) => {
        if (controller.isRunning()) count++
      })
      return count
    },
  }
}
