import { countdown, formatCountdownTime, calculateTimeDiff } from '../utils/common'
import dayjs from 'dayjs'

// 测试格式化函数
export function testFormatCountdownTime() {
  console.log('=== 测试格式化函数 ===')
  
  const testSeconds = 3661 // 1小时1分1秒
  
  console.log('标准格式:')
  console.log(`HH:mm:ss: ${formatCountdownTime(testSeconds, 'HH:mm:ss')}`) // 01:01:01
  console.log(`mm:ss: ${formatCountdownTime(testSeconds, 'mm:ss')}`) // 61:01
  console.log(`ss: ${formatCountdownTime(testSeconds, 'ss')}`) // 01
  
  console.log('\n中文格式:')
  console.log(`H时m分s秒: ${formatCountdownTime(testSeconds, 'H时m分s秒')}`) // 1时1分1秒
  console.log(`D天H时m分s秒: ${formatCountdownTime(90061, 'D天H时m分s秒')}`) // 1天1时1分1秒
  
  console.log('\n自定义格式:')
  console.log(`D天HH:mm:ss: ${formatCountdownTime(90061, 'D天HH:mm:ss')}`) // 1天01:01:01
}

// 测试时间差计算
export function testCalculateTimeDiff() {
  console.log('\n=== 测试时间差计算 ===')
  
  const now = dayjs()
  const future = now.add(2, 'hour').add(30, 'minute').add(45, 'second')
  
  const diff = calculateTimeDiff(future, now)
  console.log('时间差计算结果:')
  console.log(`总秒数: ${diff.totalSeconds}`)
  console.log(`天: ${diff.days}, 时: ${diff.hours}, 分: ${diff.minutes}, 秒: ${diff.seconds}`)
}

// 测试基础倒计时
export function testBasicCountdown() {
  console.log('\n=== 测试基础倒计时 ===')
  
  const controller = countdown({
    seconds: 10,
    format: 'mm:ss',
    onTick: ({ secondsLeft, formatted, timeLeft }) => {
      console.log(`倒计时: ${formatted} (剩余${secondsLeft}秒)`)
      console.log(`详细: ${timeLeft.minutes}分${timeLeft.seconds}秒`)
    },
    onFinish: () => {
      console.log('基础倒计时结束!')
    },
    onStart: () => {
      console.log('基础倒计时开始!')
    }
  })
  
  // 5秒后停止
  setTimeout(() => {
    console.log('手动停止倒计时')
    controller.stop()
  }, 5000)
  
  return controller
}

// 测试目标时间倒计时
export function testTargetTimeCountdown() {
  console.log('\n=== 测试目标时间倒计时 ===')
  
  // 倒计时到30秒后
  const targetTime = dayjs().add(30, 'second')
  
  const controller = countdown({
    targetTime,
    format: 'HH:mm:ss',
    onTick: ({ formatted, targetTime, currentTime }) => {
      console.log(`距离目标时间: ${formatted}`)
      console.log(`目标: ${targetTime?.format('HH:mm:ss')}, 当前: ${currentTime.format('HH:mm:ss')}`)
    },
    onFinish: () => {
      console.log('到达目标时间!')
    },
    onStart: () => {
      console.log(`开始倒计时到: ${targetTime.format('HH:mm:ss')}`)
    }
  })
  
  return controller
}

// 测试中文格式倒计时
export function testChineseFormatCountdown() {
  console.log('\n=== 测试中文格式倒计时 ===')
  
  const controller = countdown({
    seconds: 125, // 2分5秒
    format: 'm分s秒',
    onTick: ({ formatted, timeLeft }) => {
      console.log(`中文倒计时: ${formatted}`)
      console.log(`对象形式: ${timeLeft.minutes}分${timeLeft.seconds}秒`)
    },
    onFinish: () => {
      console.log('中文倒计时结束!')
    }
  })
  
  return controller
}

// 测试控制器功能
export function testControllerFunctions() {
  console.log('\n=== 测试控制器功能 ===')
  
  const controller = countdown({
    seconds: 60,
    format: 'mm:ss',
    onTick: ({ formatted }) => {
      console.log(`控制器测试: ${formatted}`)
    }
  })
  
  console.log(`初始状态 - 运行中: ${controller.isRunning()}`)
  console.log(`剩余时间: ${controller.getRemainingTime()}秒`)
  console.log(`目标时间: ${controller.getTargetTime()?.format('HH:mm:ss')}`)
  console.log(`当前时间: ${controller.getCurrentTime().format('HH:mm:ss')}`)
  
  // 10秒后重置
  setTimeout(() => {
    console.log('\n重置倒计时')
    controller.reset()
    console.log(`重置后剩余时间: ${controller.getRemainingTime()}秒`)
  }, 10000)
  
  return controller
}

// 运行所有测试
export function runAllTests() {
  console.log('🚀 开始测试 dayjs 倒计时功能\n')
  
  testFormatCountdownTime()
  testCalculateTimeDiff()
  
  // 延迟执行倒计时测试，避免输出混乱
  setTimeout(() => testBasicCountdown(), 1000)
  setTimeout(() => testTargetTimeCountdown(), 2000)
  setTimeout(() => testChineseFormatCountdown(), 3000)
  setTimeout(() => testControllerFunctions(), 4000)
}

// 如果直接运行此文件，执行所有测试
if (typeof window !== 'undefined') {
  // 浏览器环境
  (window as any).runCountdownTests = runAllTests
  console.log('在控制台中运行 runCountdownTests() 来测试倒计时功能')
}
