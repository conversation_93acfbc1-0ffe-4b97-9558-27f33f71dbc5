/*
 * @Description:
 * @Author: yucheng
 * @Date: 2024-11-11
 * @LastEditors: '余承'
 * @LastEditTime: 2025-07-13
 */
import 'virtual:uno.css'
import 'virtual:svg-icons-register'
import '@unocss/reset/normalize.css'
import '@/style/global.scss'
import { createApp } from 'vue'
import { createPinia } from 'pinia'

import App from './App.vue'
import router from './router'

const app = createApp(App)

app.use(createPinia())
app.use(router)

app.mount('#app')
