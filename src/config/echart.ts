/*
 * @Description:
 * @Author: yucheng
 * @Date: 2024-12-04
 * @LastEditors: yyy
 * @LastEditTime: 2025-04-27 11:26:00
 */
import type { EChartsOption } from 'echarts'

/**
 * 基础图表配置
 */
export const baseChartOptions: EChartsOption = {
  legend: {
    show: true,
    icon: 'rect',
    itemWidth: 8,
    itemHeight: 5,
    top: 0,
    right: 0,
    selectedMode: false,
    textStyle: {
      color: '#78AEEB',
      fontSize: 10,
      fontFamily: 'Source Han Sans CN',
    },
  },
  grid: {
    top: 22,
    left: 10,
    right: 10,
    bottom: 10,
    containLabel: true,
  },
  xAxis: {
    boundaryGap: false,
    axisLine: {
      show: true,
      lineStyle: {
        color: 'rgba(255,255,255,0.1)',
      },
    },
    axisLabel: {
      color: '#D0DEEE',
      fontSize: 10,
      hideOverlap: true,
      showMaxLabel: true,
      showMinLabel: true,
      fontFamily: 'Source Han Sans CN',
      formatter: (v) => v,
    },
    splitLine: {
      lineStyle: {
        type: 'dashed',
        color: 'rgba(255,255,255,0.1)',
      },
    },
    axisTick: {
      show: false,
    },
  },
  yAxis: {
    axisLine: {
      show: true,
      lineStyle: {
        color: 'rgba(255,255,255,0.1)',
      },
    },
    axisLabel: {
      color: '#D0DEEE',
      fontSize: 14,
      hideOverlap: true,
      showMaxLabel: true,
      showMinLabel: true,
      fontFamily: 'Source Han Sans CN',
    },
    splitLine: {
      lineStyle: {
        type: 'dashed',
        color: 'rgba(255,255,255,0.1)',
      },
    },
    axisTick: {
      show: false,
    },
  },
}
