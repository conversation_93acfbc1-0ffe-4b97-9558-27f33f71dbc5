<!--
 * @Description:
 * @Author: yucheng
 * @Date: 2025-05-21
 * @LastEditors: '余承'
 * @LastEditTime: 2025-07-15
-->
<template>
  <n-config-provider w-full h-full :theme-overrides="themeOverrides" :date-locale="dateZhCN" :locale="zhCN">
    <n-message-provider>
      <router-view></router-view>
    </n-message-provider>
  </n-config-provider>
</template>

<script lang="ts" setup>
import { NMessageProvider, NConfigProvider, dateZhCN, zhCN } from 'naive-ui'
import themeOverrides from '@/config/themeOverrides.json'
document.addEventListener('contextmenu', (e) => {
  e.preventDefault()
})

</script>

<style lang="scss">
#app {
  width: 100vw;
  height: 100vh;
  font-size: 16px;
  overflow: hidden;
  color: #fff;
  background-color: #c0c0c0;
}
</style>
