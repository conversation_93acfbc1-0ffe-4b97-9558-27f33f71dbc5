<!--
 * @Description: 3D场景查看器
 * @Author: yucheng
 * @Date: 2025-06-19
 * @LastEditors: '余承'
 * @LastEditTime: 2025-07-15
-->

<template>
  <div id="viewer" ref="viewerContainer" v-show="showScene"></div>
  <Transition name="fade-mask">
    <div class="fixed left-0 top-0 w-full h-full bg-#001a33 flex items-center justify-center" v-if="sceneLoading">
      <div class="loading-container text-center">
        <div class="loading-spinner"></div>
        <div class="text-#00e6ff text-xl font-light mt-2">
          场景加载中<span class="dot">...</span>
        </div>
      </div>
    </div>
  </Transition>
</template>

<script lang="ts" setup>
import { computed, nextTick, onMounted, ref, watch } from 'vue'
import { useTemplateRef } from 'vue'
import { initViewer, viewer } from '@/three/viewer'
import { ResourceManager } from '@/three/resource'
import * as THREE from 'three'
import { useRoute } from 'vue-router'
import { performanceMonitor } from '@/three/utils'

const route = useRoute()

const showScene = ref(true)

const sceneLoading = ref(true)

const viewerContainer = useTemplateRef('viewerContainer')

const loadModel = () => {
  const resourceManager = ResourceManager.getInstance()
  const modules = ['/models/周边建筑.glb', '/models/地形.glb', '/models/建筑.glb']
  Promise.all(modules.map((module) => resourceManager.loadGLTF(module))).then((gltfs) => {
    gltfs.forEach((gltf) => {
      gltf.traverse((child) => {
        if (child instanceof THREE.Mesh) {
          child.receiveShadow = true
        }
      })
    })
    viewer.scene.add(...gltfs)
    sceneLoading.value = false
  })
}

const initScene = () => {
  nextTick(async () => {
    if (viewerContainer.value) {
      await initViewer(viewerContainer.value)
      await loadModel()
    }
  })
}

watch(route, () => {
  if (showScene.value && !viewer) {
    initScene()
  }
})

let isPerformanceMonitor = false
onMounted(() => {
  initScene()
  window.addEventListener('keydown', (e) => {
    if (['p', 'P'].includes(e.key) && e.altKey) {
      isPerformanceMonitor = !isPerformanceMonitor
      if (isPerformanceMonitor) {
        performanceMonitor.initialize()
      } else {
        performanceMonitor.dispose()
      }
    }
  })
})
</script>

<style lang="scss" scoped>
#viewer {
  @apply fixed top-0 left-0 w-full h-full bg-black;
  // background-color: rgba(255, 255, 255, 0.5);
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 3px solid rgba(0, 230, 255, 0.2);
  border-radius: 50%;
  border-top-color: #00e6ff;
  box-shadow: 0 0 15px rgba(0, 230, 255, 0.3);
  animation: spin 1s ease-in-out infinite;
  margin: 0 auto;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.fade-mask-enter-active,
.fade-mask-leave-active {
  transition: opacity 0.3s ease;
}

.fade-mask-enter-from,
.fade-mask-leave-to {
  opacity: 0;
}

.dot {
  display: inline-block;
  height: 1em;
  line-height: 1;
  vertical-align: -0.25em;
  overflow: hidden;
}

.dot::before {
  display: block;
  content: '...\A..\A.';
  white-space: pre-wrap;
  animation: dot 3s infinite step-start both;
}

@keyframes dot {
  33% {
    transform: translateY(-2em);
  }

  66% {
    transform: translateY(-1em);
  }
}
</style>
