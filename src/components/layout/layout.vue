<!--
 * @Description:
 * @Author: yucheng
 * @Date: 2025-05-23
 * @LastEditors: '余承'
 * @LastEditTime: 2025-07-15
-->
<template>
  <div class="layout">
    <!-- <three-map /> -->
    <img src="@/assets/images/layout/border.png" alt="" class="border-img" />
    <div class="page">
      <Header />
      <router-view></router-view>
    </div>
  </div>
</template>

<script setup lang="ts">
import ThreeMap from '@/components/threeMap/threeMap.vue'
import Header from '@/components/layout/header.vue'
</script>

<style scoped lang="scss">
.layout {
  @apply relative w-full h-full;

  .border-img {
    @apply absolute top-0 left-0 w-full h-full pointer-events-none;
  }

  .page {
    @apply relative z-5;
  }
}
</style>
