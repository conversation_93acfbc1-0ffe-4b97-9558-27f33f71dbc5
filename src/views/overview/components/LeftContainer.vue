<template>
  <div class="space-y-7.5">
    <div
      class="relative w-[175px] pb-2.5 h-8 bg-no-repeat bg-center bg-cover text-[26px] font-['JiangChengXieHei-700W']"
      :style="{ backgroundImage: `url(${getAssetsImage('场景监测.png', 'overview')})` }"
    >
      <p
        class="w-[106px] h-[25px] absolute bottom-3.5 left-1/2 -translate-x-1/2 text-center"
        style="
          color: #ffffff;
          background: linear-gradient(
            0deg,
            rgba(255, 232, 199, 0.54) 0%,
            rgba(255, 255, 255, 0.54) 100%
          );
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        "
      >
        场景监测
      </p>
    </div>

    <Container title="园区数据">
      <div class="h-[210px] flex items-center gap-x-17 px-6 pt-5">
        <div v-for="(item, index) in parkData" :key="index" class="flex flex-col items-center">
          <img class="size-27.5" :src="item.icon" :alt="item.name" />
          <p class="text-[30px] font-500 text-[#32DCFB] font-['DINNextLTPro-Medium']">
            {{ item.area }}
          </p>
          <p class="name font-['AlibabaPuHuiTi'] font-italic text-base">
            {{ item.name }}
          </p>
        </div>
      </div>
    </Container>

    <Container title="值班信息">
      <div class="h-[350px] overflow-hidden pt-5 pb-4 pr-2 flex flex-col">
        <!-- 白班 -->
        <div class="flex-1">
          <div class="line"></div>
          <div class="flex items-center">
            <img class="size-35" src="@/assets/images/overview/白天.png" alt="白班  " />
            <table class="table-auto flex-1 border-collapse">
              <tbody>
                <tr>
                  <td class="pl-5">值班长:</td>
                  <td>王小明</td>
                  <td>已签到</td>
                </tr>
                <tr>
                  <td>值班员：</td>
                  <td>李四</td>
                  <td>已签到</td>
                </tr>
                <tr>
                  <td></td>
                  <td>李四</td>
                  <td>已签到</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        <div class="line"></div>

        <!-- 夜班 -->
        <div class="flex-1">
          <div class="flex items-center">
            <img class="size-35" src="@/assets/images/overview/黑夜.png" alt="晚班" />
            <table class="flex-1 border-collapse table-auto">
              <tbody>
                <tr>
                  <td>值班长:</td>
                  <td>王小明</td>
                  <td>已签到</td>
                </tr>
                <tr>
                  <td>值班员：</td>
                  <td>李四</td>
                  <td>已签到</td>
                </tr>
                <tr>
                  <td></td>
                  <td>李四</td>
                  <td>已签到</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        <div class="line"></div>
      </div>
    </Container>

    <Container title="人员信息">
      <div>
        <div class="tab-container">
          <div class="dot left-[-2px] top-[-2px]"></div>
          <div class="dot right-[-2px] top-[-2px]"></div>
          <div class="dot left-[-2px] bottom-[-2px]"></div>
          <div class="dot right-[-2px] bottom-[-2px]"></div>
          <div
            v-for="(tab, index) in tabsData"
            :key="index"
            class="tab-item w-30 h-10"
            :class="{ active: currentTabIndex === index }"
            @click="currentTabIndex = index"
          >
            {{ tab }}
          </div>
        </div>
        <n-carousel :show-dots="false">
          <n-carousel-item class="relative" v-for="(item, index) in 4" :key="index">
            <img
              class="h-[364px] w-full object-cover"
              :src="`https://naive-ui.oss-cn-beijing.aliyuncs.com/carousel-img/carousel${item}.jpeg`"
            />
            <div class="absolute bottom-0 left-0 w-full h-15 bg-[#202738]/80 flex">
              <div
                class="size-15 flex items-center justify-center leading-6 font-['PangMenZhengDao']"
                style="
                  background: linear-gradient(45deg, rgba(50, 220, 251, 0.39) 0%);
                  border: 1px solid;
                  opacity: 0.8;
                  border-image: linear-gradient(45deg, #32dcfb, #ffffff) 10 10;
                "
              >
                科技<br />之星
              </div>
              <div
                class="text-white text-[23px] leading-[38px] flex-1 flex items-center justify-center"
              >
                播出机房：陈喆光
              </div>
            </div>
          </n-carousel-item>
        </n-carousel>
      </div>
    </Container>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { getAssetsImage } from '@/utils/common'
import { NCarousel, NCarouselItem } from 'naive-ui'
import Container from '@/components/Container/Container.vue'

// 园区数据
const parkData = [
  {
    name: '总占地面积',
    area: '2205㎡',
    icon: getAssetsImage('总占地面积.png', 'overview'),
  },
  {
    name: '建筑面积',
    area: '1287㎡',
    icon: getAssetsImage('建筑面积.png', 'overview'),
  },
  {
    name: '绿化面积',
    area: '825㎡',
    icon: getAssetsImage('绿化面积.png', 'overview'),
  },
]

const currentTabIndex = ref(0)
const tabsData = ['功勋荣誉', '学历结构']
</script>

<style scoped lang="scss">
.name {
  color: #d1d6df;
  background: linear-gradient(0deg, #acddff 0%, #ffffff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.line {
  margin-left: 24px;
  width: 506px;
  height: 1px;
  background-color: rgba(255, 255, 255, 0.74);
}

tr {
  height: 40px;
  background: linear-gradient(90deg, rgba(27, 130, 183, 0.22) 0%, rgba(35, 104, 162, 0) 100%);
  border-bottom: 1px solid rgba(216, 227, 238, 0.1);
  &:last-child {
    border-bottom: none;
  }
  td:first-child {
    padding-left: 24px;
  }
  td {
    font-family: 'AlibabaPuHuiTi';
  }
}

.tab-container {
  position: relative;
  display: flex;
  align-items: center;
  padding: 0 6px;
  column-gap: 5px;
  width: 520px;
  height: 50px;
  border: 1px solid;
  opacity: 0.5;
  border-image: linear-gradient(0deg, #81c5c8, #81c5c8) 10 10;
}

.tab-item {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  color: #fff;
  background-image: linear-gradient(0deg, rgba(42, 215, 255, 0.2) 0%, rgba(42, 215, 255, 0) 100%);
  border: 1px solid;
  border-image: linear-gradient(0deg, #81c5c8, #81c5c8) 10 10;
  cursor: pointer;
  &.active {
    background-image: url('@/assets/images/overview/tab_bg.png') !important;
    background-size: 100% 100%;
    background-repeat: no-repeat;
    background-position: center;
    border: none !important;
  }
}

.dot {
  @apply absolute size-[5px] rounded-full bg-white;
}
</style>
