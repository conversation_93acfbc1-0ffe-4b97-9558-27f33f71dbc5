<template>
  <div class="space-y-7.5">
    <div class="flex items-center gap-x-8 justify-end">
      <img src="@/assets/images/overview/消息.png" alt="" class="size-10" />
      <div class="w-[3px] h-9 bg-[#9EC5E7]"></div>
      <img src="@/assets/images/overview/个人中心.png" alt="" class="size-10" />
    </div>
    <Container title="环境信息">
      <div class="h-45 pl-2.5 pt-7.5">
        <div
          class="flex items-center gap-x-6 font-['ShiShangZhongHeiJianTi'] leading-[27px] text-[22px] text-[#CDD6E3]"
        >
          <span>{{ date }}</span>
          <span>{{ time }}</span>
          <span>{{ week }}</span>
        </div>
        <div class="flex items-center gap-x-10 mt-6">
          <div class="flex flex-col items-center" v-for="(item, index) in envListData" :key="index">
            <img :src="item.icon" :alt="item.label" class="size-[50px]" />
            <span class="text-lg text-white font-['AlibabaPuHuiTi']">{{ item.label }}</span>
          </div>
        </div>
      </div>
    </Container>

    <Container title="播出数据">
      <div class="h-95 pt-2 px-3">
        <!-- 上行播出数据 -->
        <div>
          <!-- 标题 -->
          <div
            class="flex items-center justify-between font-italic font-500 text-lg text-[#D1D6DF]"
          >
            <div class="h-7.5 w-75">
              <span class="title inline-block">上行播出数据</span>
            </div>
            <div class="title">主系统</div>
          </div>
          <!-- 内容 -->
          <div class="grid grid-cols-4 gap-x-9 gap-y-4 pt-4">
            <div class="flex flex-col" v-for="(item, index) in upPlayData" :key="index">
              <span class="text-lg font-400 text-white">{{ item.label }}</span>
              <span class="text-[26px] font-['PangMenZhengDao'] text-[#FFD631] -mt-1">
                {{ item.value }}
              </span>
              <img src="@/assets/images/overview/line.png" alt="" class="w-[102px] h-2 mt-1" />
            </div>
          </div>
        </div>
        <!-- 下行播出数据 -->
        <div class="mt-4">
          <!-- 标题 -->
          <div
            class="flex items-center justify-between font-italic font-500 text-lg text-[#D1D6DF]"
          >
            <div class="h-7.5 w-75">
              <span class="title inline-block">下行播出数据</span>
            </div>
          </div>
          <!-- 内容 -->
          <div class="flex items-center justify-center gap-x-[114px]">
            <div class="flex items-center flex-col">
              <div class="size-24 bg-[#62DCFF]/10 rounded-full">
                <Echart :options="options" />
              </div>
              <div class="text-white font-['AlibabaPuHuiTi'] text-lg">信噪比</div>
            </div>

            <div class="flex items-center flex-col">
              <div class="size-24 bg-[#62DCFF]/10 rounded-full">
                <Echart :options="options" />
              </div>
              <div class="text-white font-['AlibabaPuHuiTi'] text-lg">误码率</div>
            </div>
          </div>
        </div>
      </div>
    </Container>

    <Container title="提醒信息">
      <div>
        <div>
          <div>
            <div class="flex items-center gap-x-5">
              <span>倒计时:</span>
              <div class="h-11 px-6 bg-black/39 border-1 border-solid border-[#32DCFB]">
                10:32:09
              </div>
            </div>
          </div>
          <div></div>
        </div>
      </div>
    </Container>
  </div>
</template>

<script setup lang="ts">
import { onBeforeMount, ref, onBeforeUnmount } from 'vue'
import dayjs from 'dayjs'
import { getAssetsImage, countdown } from '@/utils/common'
import * as echarts from 'echarts'
import type { EChartsOption } from 'echarts'
import Container from '@/components/Container/Container.vue'
import Echart from '@/components/Echart/Echart.vue'

// 年月日
const date = ref()
const time = ref()
const week = ref()

const weekMap = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六']

let timer: NodeJS.Timeout | null = null

const envListData = [
  {
    label: '无雨雪',
    icon: getAssetsImage('无雨雪.png'),
  },
  {
    label: '24℃',
    icon: getAssetsImage('气温.png'),
  },
  {
    label: '35%',
    icon: getAssetsImage('湿度.png'),
  },
  {
    label: '1013hPa',
    icon: getAssetsImage('气压.png'),
  },
  {
    label: '12m/s',
    icon: getAssetsImage('风速.png'),
  },
  {
    label: '东南',
    icon: getAssetsImage('风向.png'),
  },
]

countdown({
  seconds: 60 * 60 * 24,
  format: 'HH:mm:ss',
  onTick: ({ secondsLeft, formatted }) => {
    console.log(secondsLeft, formatted)
  },
  onFinish: () => {
    console.log('倒计时结束')
  },
})

const upPlayData = [
  {
    label: '上行功率',
    value: '48.9',
  },
  {
    label: '方位角',
    value: '181.83',
  },
  {
    label: '俯仰角',
    value: '1.00',
  },
  {
    label: '极化角',
    value: '1.00',
  },
  {
    label: '信标电平',
    value: '-10.0',
  },
  {
    label: '电平偏差',
    value: '1000',
  },
  {
    label: '符号率',
    value: '32.6',
  },
  {
    label: '码率',
    value: '46.7',
  },
]

const options: EChartsOption = {
  title: {
    show: true,
    text: '32{percent|%}',
    left: 'center',
    top: '34%',
    textStyle: {
      fontWeight: 'bold',
      fontFamily: 'DINNextLTPro',
      fontSize: 29,
      color: '#50E0FF',
      rich: {
        percent: {
          fontWeight: 'bold',
          fontFamily: 'DINNextLTPro',
          fontSize: 14,
          color: '#50E0FF',
          verticalAlign: 'middle',
        },
      },
    },
  },
  series: [
    {
      radius: '88%',
      type: 'gauge',
      startAngle: 360,
      endAngle: 0,
      pointer: {
        show: false,
      },
      progress: {
        width: 8,
        show: true,
        overlap: true,
        roundCap: false,
        clip: false,
      },
      axisLine: {
        show: false,
      },
      splitLine: {
        show: false,
      },
      axisTick: {
        show: false,
      },
      axisLabel: {
        show: false,
      },
      data: [
        {
          value: 20,
          name: '',
          title: {
            show: false,
          },
          detail: {
            show: false,
          },
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: '#1DA7C0' },
              { offset: 1, color: '#C5FF87' },
            ]),
          },
        },
        {
          value: 100,
          name: '',
          title: {
            show: false,
          },
          detail: {
            show: false,
          },
          itemStyle: {
            color: '#507190',
          },
        },
      ],
    },
  ],
}

onBeforeMount(() => {
  renderCountdown()
})

onBeforeUnmount(() => {
  timer && clearTimeout(timer)
})

function renderCountdown() {
  date.value = dayjs().format('YYYY/MM/DD')
  time.value = dayjs().format('HH:mm:ss')
  week.value = weekMap[Number(dayjs().format('d'))]

  timer = setTimeout(() => {
    renderCountdown()
  }, 1000)
}
</script>

<style scoped lang="scss">
.title {
  @include text-gradual(#acddff, #fff);
}
</style>
