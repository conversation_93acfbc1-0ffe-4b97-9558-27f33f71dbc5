<template>
  <div class="overview">
    <div class="left-container">
      <LeftContainer />
    </div>
    <div class="right-container">
      <RightContainer />
    </div>
  </div>
  <div class="fixed bottom-8 w-full">
    <div class="flex items-center justify-center gap-x-[145px]">
      <div
        v-for="(item, index) in footerList"
        :key="index"
        class="size-40 bg-cover bg-center flex items-center justify-center"
        :style="{ backgroundImage: `url(${item.icon})` }"
      >
        <span class="font-['JiangChengXieHei-700W'] text-[26px] text-[#C5DFEF] text-center mt-10">
          {{ item.label }}
        </span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { getAssetsImage } from '@/utils/common'
import LeftContainer from './components/LeftContainer.vue'
import RightContainer from './components/RightContainer.vue'

const footerList = [
  {
    label: '地球站全景',
    icon: getAssetsImage('地球站全景.png'),
  },
  {
    label: '节目覆盖',
    icon: getAssetsImage('节目覆盖.png'),
  },
  {
    label: '机房布局',
    icon: getAssetsImage('机房布局.png'),
  },
  {
    label: '系统拓补',
    icon: getAssetsImage('系统拓补.png'),
  },
]
</script>

<style scoped lang="scss">
.left-container,
.right-container {
  @apply absolute top-0 h-screen pt-[90px] bg-black/30;
}
.left-container {
  @apply left-0 pl-[70px];
}
.right-container {
  @apply right-0 pr-[70px];
}
</style>
