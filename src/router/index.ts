/*
 * @Description:
 * @Author: yucheng
 * @Date: 2025-06-18
 * @LastEditors: '余承'
 * @LastEditTime: 2025-07-13
 */
/*
 * @Description: 路由配置文件
 * @Author: yucheng
 * @Date: 2025-06-18
 * @LastEditors: 余承
 * @LastEditTime: 2025-06-24
 */
import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'
import Layout from '@/components/layout/layout.vue'

export const routes: RouteRecordRaw[] = [
  {
    path: '/',
    component: Layout,
    redirect: '/overview',
    children: [
      {
        path: 'overview',
        name: 'AreaOverview',
        component: () => import('@/views/overview/overview.vue'),
        meta: {
          title: '园区全景',
        },
      },
    ],
  },
]

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes,
})

router.beforeEach(async (to, from, next) => {
  if (typeof from.meta.clearScene === 'function') {
    await from.meta.clearScene()
  }
  if (typeof to.meta.initScene === 'function') {
    await to.meta.initScene()
  }
  next()
})

export default router
