{"name": "nami-demo", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build", "lint": "eslint . --fix", "format": "prettier --write src/"}, "dependencies": {"@tweenjs/tween.js": "^25.0.0", "@types/lodash-es": "^4.17.12", "@types/three": "^0.176.0", "@unocss/reset": "^66.1.2", "@vueuse/core": "^13.2.0", "axios": "^1.9.0", "dayjs": "^1.11.13", "echarts": "^5.6.0", "lodash-es": "^4.17.21", "naive-ui": "^2.41.0", "pinia": "^3.0.1", "three": "^0.176.0", "vue": "^3.5.13", "vue-router": "^4.5.0"}, "devDependencies": {"@tsconfig/node22": "^22.0.1", "@types/node": "^22.14.0", "@unocss/preset-attributify": "^66.1.2", "@unocss/preset-uno": "^66.1.2", "@vitejs/plugin-vue": "^5.2.3", "@vue/eslint-config-prettier": "^10.2.0", "@vue/eslint-config-typescript": "^14.5.0", "@vue/tsconfig": "^0.7.0", "eslint": "^9.22.0", "eslint-plugin-vue": "~10.0.0", "jiti": "^2.4.2", "npm-run-all2": "^7.0.2", "prettier": "3.5.3", "sass": "^1.89.0", "typescript": "~5.8.0", "unocss": "^66.1.2", "vite": "^6.2.4", "vite-plugin-svg-icons": "^2.0.1", "vite-plugin-vue-devtools": "^7.7.2", "vue-tsc": "^2.2.8"}}