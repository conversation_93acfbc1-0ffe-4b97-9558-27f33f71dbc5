# nami-demo

This template should help get you started developing with Vue 3 in Vite.

## Recommended IDE Setup

[VSCode](https://code.visualstudio.com/) + [Volar](https://marketplace.visualstudio.com/items?itemName=Vue.volar) (and disable Vetur).

## Type Support for `.vue` Imports in TS

TypeScript cannot handle type information for `.vue` imports by default, so we replace the `tsc` CLI with `vue-tsc` for type checking. In editors, we need [Volar](https://marketplace.visualstudio.com/items?itemName=Vue.volar) to make the TypeScript language service aware of `.vue` types.

## Customize configuration

See [Vite Configuration Reference](https://vite.dev/config/).

## Project Setup

```sh
pnpm install
```

### Compile and Hot-Reload for Development

```sh
pnpm dev
```

### Type-Check, Compile and Minify for Production

```sh
pnpm build
```

### Lint with [ESLint](https://eslint.org/)

```sh
pnpm lint
```

## ThreeJS事件管理模块

项目中集成了一个基于发布订阅模式的ThreeJS事件管理模块，用于统一管理3D场景中的各类事件。

### 主要功能

- 集中管理ThreeJS的各类事件（相机、控制器、鼠标、对象等）
- 提供统一的事件订阅和发布接口
- 支持一次性事件订阅
- 自动处理对象交互（悬停、点击、双击等）

### 使用方法

#### 1. 初始化事件管理器

事件管理器在`viewer`初始化时会自动初始化，无需手动调用。

```typescript
import { initViewer } from '@/common/viewer'
import { threeJSEvents } from '@/common/evens'

// 初始化viewer时会自动初始化事件管理器
initViewer(container)
```

#### 2. 订阅事件

```typescript
import { threeJSEvents } from '@/common/evens'

// 订阅相机移动事件
threeJSEvents.subscribe('camera:move', (data) => {
  console.log('相机位置:', data.position)
})

// 订阅对象点击事件
threeJSEvents.subscribe('object:click', (data) => {
  console.log('点击对象:', data.object)
})

// 订阅一次性事件
threeJSEvents.subscribe('model:loaded', (data) => {
  console.log('模型加载完成:', data.model)
}, true) // 第三个参数为true表示只触发一次
```

#### 3. 取消订阅

```typescript
const handleCameraMove = (data) => {
  console.log('相机位置:', data.position)
}

// 订阅事件
threeJSEvents.subscribe('camera:move', handleCameraMove)

// 取消订阅
threeJSEvents.unsubscribe('camera:move', handleCameraMove)
```

#### 4. 通过viewer简化调用

```typescript
import { viewer } from '@/common/viewer'

// 使用viewer.on方法订阅事件
viewer.on('camera:move', (data) => {
  console.log('相机位置:', data.position)
})

// 使用viewer.off方法取消订阅
viewer.off('camera:move', handleCameraMove)
```

### 支持的事件类型

#### 相机事件
- `camera:change`: 相机参数变化
- `camera:zoom`: 相机缩放变化
- `camera:move`: 相机位置变化

#### 控制器事件
- `controls:start`: 控制器操作开始
- `controls:end`: 控制器操作结束
- `controls:change`: 控制器参数变化

#### 鼠标事件
- `mouse:move`: 鼠标移动
- `mouse:click`: 鼠标点击
- `mouse:dblclick`: 鼠标双击

#### 对象事件
- `object:hover`: 鼠标悬停在对象上
- `object:click`: 鼠标点击对象
- `object:dblclick`: 鼠标双击对象
- `object:leave`: 鼠标离开对象

#### 场景事件
- `scene:loaded`: 场景加载完成
- `scene:update`: 场景更新（每帧）
- `scene:resize`: 场景大小变化

#### 模型事件
- `model:loaded`: 模型加载完成
- `model:error`: 模型加载失败

## 标签管理模块使用说明

标签管理模块(`src/common/label.ts`)提供了在Three.js场景中添加、更新和移除HTML标签的功能。标签元素使用CSS2DRenderer创建，会跟随场景移动，保持在3D空间中的固定位置。

### 基本用法

```typescript
import { labelManager } from '@/common/label'

// 创建一个简单的文本标签
const label = labelManager.createLabel({
  position: [0, 5, 0], // 或者使用 new THREE.Vector3(0, 5, 0)
  content: '这是一个标签',
  style: {
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    color: '#ffffff',
    padding: '8px 12px',
    borderRadius: '4px',
  }
})

// 更新标签位置
labelManager.updateLabelPosition(label, [10, 5, 0])

// 更新标签内容
labelManager.updateLabelContent(label, '更新后的内容')

// 更新标签样式
labelManager.updateLabelStyle(label, {
  backgroundColor: 'rgba(0, 120, 255, 0.8)',
})

// 设置标签可见性
labelManager.setLabelVisible(label, false)

// 移除单个标签
labelManager.removeLabel(label)

// 移除所有标签
labelManager.removeAllLabels()
```

### 高级用法

#### 创建带HTML内容的标签

```typescript
// 创建自定义HTML元素
const customElement = document.createElement('div')
customElement.innerHTML = `
  <div style="text-align: center;">
    <h3 style="margin: 0 0 8px 0; color: #00e6ff;">标题</h3>
    <p style="margin: 0; font-size: 12px;">详细信息</p>
  </div>
`

// 创建标签
const label = labelManager.createLabel({
  position: [0, 5, 0],
  content: customElement,
})
```

#### 创建带交互事件的标签

```typescript
const label = labelManager.createLabel({
  position: [0, 5, 0],
  content: '点击查看详情',
  style: {
    cursor: 'pointer',
  },
  onClick: (event) => {
    console.log('标签被点击', event)
    // 添加交互逻辑
  },
  onPointerEnter: (event) => {
    // 鼠标悬停效果
    const element = event.target as HTMLElement
    element.style.backgroundColor = 'rgba(0, 120, 255, 0.8)'
  },
  onPointerLeave: (event) => {
    // 鼠标离开效果
    const element = event.target as HTMLElement
    element.style.backgroundColor = 'rgba(0, 0, 0, 0.7)'
  }
})
```

### 示例代码

查看 `src/common/examples/labelExample.ts` 获取更多使用示例。
